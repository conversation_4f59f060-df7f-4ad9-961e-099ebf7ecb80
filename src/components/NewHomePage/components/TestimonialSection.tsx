"use client";

import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import {
  BodyLarge,
  BodyMedium,
  BodySmall,
  HeadingLarge,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { ArrowRight } from "lucide-react";
import TestimonialCard from "./TestimonialCard";

export type TestimonialSectionProps = {
  pill_Content: string;
  title: string;
  story: {
    title: string;
    points: string[];
  };
  testimonials: {
    name: string;
    content?: string;
    video_url?: string;
  }[];
};

const TestimonialSection = ({
  pill_Content,
  title,
  story,
  testimonials,
}: TestimonialSectionProps) => {
  const storyPoints = story.points;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const testimonialRef = useRef<HTMLDivElement>(null);
  const mobileScrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % storyPoints.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [storyPoints.length]);

  // Detect mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Get the style for each position in the list animation
  const getPositionStyle = (position: number) => {
    switch (position) {
      case 0:
        return "opacity-30 text-primary-600 bg-transparent";
      case 1:
        return "opacity-60 text-primary-700 bg-transparent";
      case 2:
        return "opacity-100 text-white bg-secondary-400 py-3 md:py-5 px-4 md:px-9 shadow-md";
      case 3:
        return "opacity-60 text-primary-700 bg-transparent ";
      case 4:
        return "opacity-30 text-primary-600 bg-transparent";
      default:
        return "opacity-0 scale-90";
    }
  };

  // Scroll-based animation for testimonial slider
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current || !testimonialRef.current) return;

      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      const scrollStart = -rect.top;
      const scrollEnd = rect.height - windowHeight;

      if (scrollStart > 0 && scrollStart < scrollEnd) {
        const progress = scrollStart / scrollEnd;
        setScrollProgress(progress);
      } else if (scrollStart <= 0) {
        setScrollProgress(0);
      } else if (scrollStart >= scrollEnd) {
        setScrollProgress(1);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Sync desktop scroll progress with currentTestimonialIndex
  useEffect(() => {
    if (testimonials.length > 1) {
      const progressPerTestimonial = 1 / (testimonials.length - 1);
      const calculatedIndex = Math.round(
        scrollProgress / progressPerTestimonial
      );
      setCurrentTestimonialIndex(
        Math.max(0, Math.min(calculatedIndex, testimonials.length - 1))
      );
    }
  }, [scrollProgress, testimonials.length]);

  // Calculate max scroll based on viewport
  const desktopCardWidth = 420; // TestimonialCard w-[426px] on desktop
  const desktopGap = 32; // gap-8 = 32px
  const mobileCardWidth = 300; // Container w-[300px] on mobile (card is 278px but container is 300px)
  const mobileGap = 16; // gap-4 = 16px

  const desktopMaxScroll =
    testimonials.length * desktopCardWidth +
    (testimonials.length - 1) * desktopGap -
    500;
  const mobileMaxScroll =
    testimonials.length * mobileCardWidth +
    (testimonials.length - 1) * mobileGap -
    (typeof window !== "undefined" && isMobile ? window.innerWidth - 32 : 375); // Subtract container padding

  const maxScroll = isMobile ? mobileMaxScroll : desktopMaxScroll;
  const translateX = -(scrollProgress * maxScroll);
  const scrollHeight =
    typeof window !== "undefined" ? window.innerHeight * 3 : 3000;

  // Function to scroll to next testimonial
  const scrollToNextTestimonial = () => {
    if (currentTestimonialIndex < testimonials.length - 1) {
      const nextIndex = currentTestimonialIndex + 1;
      setCurrentTestimonialIndex(nextIndex);

      // Update scroll progress for both mobile and desktop
      if (testimonials.length > 1) {
        const progressPerTestimonial = 1 / (testimonials.length - 1);
        const newProgress = Math.min(nextIndex * progressPerTestimonial, 1);
        setScrollProgress(newProgress);
      }
    }
  };

  return (
    <div
      ref={containerRef}
      style={{ height: `${scrollHeight}px` }}
      className="mb-9 md:mb-24"
    >
      <div className="sticky top-0 md:top-28 bg-secondary-100 pt-10 w-full overflow-hidden pb-16">
        <SectionContainer className="flex flex-col mb-6 md:mb-16 items-center gap-2">
          <Pill
            pill={pill_Content}
            border={false}
            textColor="text-secondary-400"
            bgColor="bg-white"
            className="mb-3"
          />
          <HeadingXLarge className="text-primary-800 text-center font-medium">
            {title}
          </HeadingXLarge>
        </SectionContainer>

        {/* ------------------ Main Content ------------------ */}
        <div className="flex flex-col items-center md:flex-row gap-6 md:gap-11 w-full px-6 md:px-0">
          {/* ----------- Story Section ----------- */}
          <div className="w-full md:w-3/5 flex flex-col items-end">
            <div className="flex flex-col-reverse md:flex-col  md:gap-4 md:pl-28 w-full justify-center items-center md:items-start">
              <HeadingLarge className="text-primary-800 font-semibold inline-flex items-center gap-1.5">
                {story.title}
                <span>
                  <Image
                    src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b1b-025e-7670-869e-d2e797eb7ec7/Untitled_Artwork 40 2.png"
                    alt="brush stroke"
                    width={40}
                    height={40}
                  />
                </span>
              </HeadingLarge>

              {/* Rotating Story Points */}
              <div className="relative max-h-[300px] flex items-center overflow-hidden w-full ">
                <div className="w-full relative min-h-[150px] md:min-h-[300px] flex flex-col gap-3 md:gap-6 items-center md:items-start">
                  {storyPoints.map((point, index) => {
                    const relativePosition =
                      (index - currentIndex + storyPoints.length) %
                      storyPoints.length;
                    const position =
                      relativePosition < 5 ? relativePosition : -1;
                    const isVisible = position >= 0;
                    const multiplier = isMobile ? 25 : 50;
                    const topPosition =
                      position >= 3
                        ? position * multiplier + multiplier
                        : position * multiplier;

                    return (
                      <div
                        key={index}
                        className={`absolute left-0 right-0 transition-all duration-700 ease-in-out ${
                          isVisible ? "block" : "hidden"
                        }`}
                        style={{ top: `${topPosition}px` }}
                      >
                        <div
                          className={`rounded-full text-center md:text-start transition-all duration-700 ease-in-out ${getPositionStyle(
                            position
                          )}`}
                        >
                          {isMobile ? (
                            <BodySmall
                              className={position === 2 ? "font-medium" : ""}
                            >
                              {point}
                            </BodySmall>
                          ) : position === 2 ? (
                            <BodyLarge weight="medium">{point}</BodyLarge>
                          ) : (
                            <BodyMedium>{point}</BodyMedium>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* ----------- Testimonials Section ----------- */}
          <div className="w-full  md:w-2/5">
            <div className="flex overflow-hidden md:p-6 md:bg-white md:rounded-l-xl justify-center md:justify-start">
              {/* Desktop horizontal scroll animation */}
              <div
                ref={testimonialRef}
                className="hidden md:flex gap-8 transition-transform h-full duration-100 ease-out"
                style={{ transform: `translateX(${translateX}px)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <TestimonialCard
                    key={index}
                    name={testimonial.name}
                    statement={testimonial.content}
                    video_url={testimonial.video_url}
                  />
                ))}
              </div>

              {/* Mobile horizontal scroll (scroll-based animation) */}
              <div
                ref={mobileScrollRef}
                className="flex md:hidden gap-5 transition-transform duration-100 ease-out pb-4 w-full"
                style={{ transform: `translateX(${translateX}px)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <TestimonialCard
                    key={index}
                    name={testimonial.name}
                    statement={testimonial.content}
                    video_url={testimonial.video_url}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* View More Testimonials Button */}
        {/* <div className="w-full flex md:flex-row items-center justify-center md:justify-end mb-6 md:mb-16 md:mt-8 md:!pr-32">
          <button
            onClick={scrollToNextTestimonial}
            disabled={currentTestimonialIndex >= testimonials.length - 1}
            className="flex items-center gap-2 text-primary-400 font-medium hover:text-primary-500 transition-colors disabled:cursor-not-allowed"
          >
            <span>View More Testimonials</span>
            <ArrowRight className="w-5 h-5" />
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default TestimonialSection;
